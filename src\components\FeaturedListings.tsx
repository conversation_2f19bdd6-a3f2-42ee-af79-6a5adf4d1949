import { motion } from 'framer-motion';
import PropertyCard from './PropertyCard';
import property1 from '@/assets/property-1.jpg';
import property2 from '@/assets/property-2.jpg';
import property3 from '@/assets/property-3.jpg';

const properties = [
  {
    id: 1,
    image: property1,
    price: '$8,500,000',
    location: 'Manhattan Penthouse, New York'
  },
  {
    id: 2,
    image: property2,
    price: '$12,200,000',
    location: 'Malibu Oceanfront, California'
  },
  {
    id: 3,
    image: property3,
    price: '$6,800,000',
    location: 'Aspen Mountain Retreat, Colorado'
  },
  {
    id: 4,
    image: property1,
    price: '$15,300,000',
    location: 'Beverly Hills Estate, California'
  },
  {
    id: 5,
    image: property2,
    price: '$9,700,000',
    location: 'Hamptons Waterfront, New York'
  }
];

const FeaturedListings = () => {
  return (
    <motion.section 
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true }}
      transition={{ duration: 0.8 }}
      className="py-24 bg-primary"
    >
      <div className="container mx-auto px-4">
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="font-heading text-5xl md:text-6xl text-center text-text-primary mb-16 tracking-luxury"
        >
          Curated Collections.
        </motion.h2>
        
        {/* Horizontal Scrolling Carousel */}
        <motion.div 
          drag="x"
          dragConstraints={{ left: -1200, right: 0 }}
          className="flex cursor-grab active:cursor-grabbing"
          whileDrag={{ cursor: 'grabbing' }}
        >
          {properties.map((property) => (
            <PropertyCard
              key={property.id}
              image={property.image}
              price={property.price}
              location={property.location}
            />
          ))}
        </motion.div>
        
        <div className="text-center mt-12">
          <p className="font-body text-text-primary/60 text-sm tracking-wide">
            Drag to explore our exclusive portfolio
          </p>
        </div>
      </div>
    </motion.section>
  );
};

export default FeaturedListings;