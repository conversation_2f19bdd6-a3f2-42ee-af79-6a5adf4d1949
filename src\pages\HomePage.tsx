import { motion, AnimatePresence } from 'framer-motion';
import HeroSection from '@/components/HeroSection';
import FeaturedListings from '@/components/FeaturedListings';
import AgentProfile from '@/components/AgentProfile';
import Footer from '@/components/Footer';

const HomePage = () => {
  return (
    <AnimatePresence>
      <motion.main
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.5 }}
        className="min-h-screen"
      >
        <HeroSection />
        <FeaturedListings />
        <AgentProfile />
        <Footer />
      </motion.main>
    </AnimatePresence>
  );
};

export default HomePage;